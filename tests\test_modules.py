"""
数字人语音诊疗系统测试模块
Test modules for Digital Human Voice Medical Diagnosis System
"""

import unittest
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.ollama_client import OllamaClient
from modules.medical_diagnosis import MedicalDiagnosisSystem
from prompts.medical_prompts import MedicalPrompts

class TestOllamaClient(unittest.TestCase):
    """测试Ollama客户端"""
    
    def setUp(self):
        """设置测试环境"""
        self.client = OllamaClient()
    
    def test_connection(self):
        """测试连接"""
        # 注意：这个测试需要Ollama服务运行
        result = self.client.check_connection()
        print(f"Ollama连接状态: {result}")
    
    def test_model_availability(self):
        """测试模型可用性"""
        result = self.client.check_model_availability()
        print(f"模型可用性: {result}")

class TestMedicalPrompts(unittest.TestCase):
    """测试医疗提示词"""
    
    def test_diagnosis_prompt(self):
        """测试诊断提示词生成"""
        symptoms = "头痛、发热、咳嗽"
        prompt = MedicalPrompts.get_diagnosis_prompt(symptoms)
        
        self.assertIn(symptoms, prompt)
        self.assertIn("可能的疾病", prompt)
        self.assertIn("建议的检查", prompt)
        print("诊断提示词测试通过")
    
    def test_emergency_prompt(self):
        """测试紧急情况提示词"""
        symptoms = "胸痛、呼吸困难"
        prompt = MedicalPrompts.get_emergency_prompt(symptoms)
        
        self.assertIn(symptoms, prompt)
        self.assertIn("紧急情况", prompt)
        print("紧急情况提示词测试通过")

class TestMedicalDiagnosis(unittest.TestCase):
    """测试医疗诊断系统"""
    
    def setUp(self):
        """设置测试环境"""
        self.diagnosis_system = MedicalDiagnosisSystem()
    
    def test_system_status(self):
        """测试系统状态"""
        status = self.diagnosis_system.check_system_status()
        print(f"系统状态: {status}")
    
    def test_emergency_detection(self):
        """测试紧急情况检测"""
        # 测试紧急情况
        emergency_symptoms = "胸痛、呼吸困难、昏迷"
        is_emergency = self.diagnosis_system.detect_emergency(emergency_symptoms)
        print(f"紧急情况检测 ('{emergency_symptoms}'): {is_emergency}")
        
        # 测试非紧急情况
        normal_symptoms = "轻微头痛、疲劳"
        is_emergency = self.diagnosis_system.detect_emergency(normal_symptoms)
        print(f"非紧急情况检测 ('{normal_symptoms}'): {is_emergency}")
    
    def test_symptom_preprocessing(self):
        """测试症状预处理"""
        raw_symptoms = "  头痛   发热  咳嗽  "
        processed = self.diagnosis_system.preprocess_symptoms(raw_symptoms)
        
        self.assertEqual(processed, "头痛 发热 咳嗽。")
        print("症状预处理测试通过")

def run_basic_tests():
    """运行基础测试"""
    print("=" * 50)
    print("数字人语音诊疗系统 - 基础功能测试")
    print("=" * 50)
    
    # 测试提示词
    print("\n1. 测试医疗提示词...")
    suite = unittest.TestLoader().loadTestsFromTestCase(TestMedicalPrompts)
    unittest.TextTestRunner(verbosity=2).run(suite)
    
    # 测试诊断系统
    print("\n2. 测试医疗诊断系统...")
    suite = unittest.TestLoader().loadTestsFromTestCase(TestMedicalDiagnosis)
    unittest.TextTestRunner(verbosity=2).run(suite)
    
    # 测试Ollama客户端（需要服务运行）
    print("\n3. 测试Ollama客户端...")
    print("注意：此测试需要Ollama服务正在运行")
    suite = unittest.TestLoader().loadTestsFromTestCase(TestOllamaClient)
    unittest.TextTestRunner(verbosity=2).run(suite)

def run_integration_test():
    """运行集成测试"""
    print("\n" + "=" * 50)
    print("集成测试 - 完整诊断流程")
    print("=" * 50)
    
    try:
        # 初始化诊断系统
        diagnosis_system = MedicalDiagnosisSystem()
        
        # 检查系统状态
        status = diagnosis_system.check_system_status()
        print(f"系统状态检查: {status}")
        
        if not all(status.values()):
            print("警告：部分系统组件不可用，集成测试可能失败")
        
        # 测试症状
        test_symptoms = "头痛、发热38度、咳嗽、乏力"
        print(f"\n测试症状: {test_symptoms}")
        
        # 生成诊断
        print("正在生成诊断...")
        result = diagnosis_system.generate_diagnosis(test_symptoms)
        
        if result["success"]:
            print("✅ 诊断生成成功")
            print(f"紧急程度: {'是' if result['is_emergency'] else '否'}")
            print(f"诊断结果预览: {result['diagnosis'][:100]}...")
        else:
            print(f"❌ 诊断生成失败: {result['error']}")
            
    except Exception as e:
        print(f"❌ 集成测试异常: {e}")

if __name__ == "__main__":
    # 运行基础测试
    run_basic_tests()
    
    # 运行集成测试
    run_integration_test()
    
    print("\n" + "=" * 50)
    print("测试完成！")
    print("=" * 50)
