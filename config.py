"""
数字人语音诊疗系统配置文件
Digital Human Voice Medical Diagnosis System Configuration
"""

import os
from typing import Dict, Any

class Config:
    """系统配置类"""
    
    # Ollama服务配置
    OLLAMA_BASE_URL = "http://localhost:11434"
    OLLAMA_MODEL_NAME = "deepseek-r1:1.5b"
    OLLAMA_TIMEOUT = 30  # 请求超时时间（秒）
    
    # 语音识别配置
    SPEECH_RECOGNITION_TIMEOUT = 5  # 语音识别超时时间（秒）
    SPEECH_RECOGNITION_PHRASE_TIMEOUT = 1  # 短语超时时间（秒）
    SPEECH_RECOGNITION_LANGUAGE = "zh-CN"  # 识别语言
    
    # 语音合成配置
    TTS_RATE = 150  # 语音速度
    TTS_VOLUME = 0.8  # 音量 (0.0-1.0)
    TTS_VOICE_INDEX = 0  # 语音索引（0为默认）
    
    # 界面配置
    WINDOW_TITLE = "数字人语音诊疗系统"
    WINDOW_WIDTH = 800
    WINDOW_HEIGHT = 600
    
    # 日志配置
    LOG_LEVEL = "INFO"
    LOG_FILE = "medical_diagnosis.log"
    
    # 医疗诊断配置
    MAX_RESPONSE_LENGTH = 1000  # 最大响应长度
    DIAGNOSIS_TIMEOUT = 30  # 诊断超时时间（秒）
    
    @classmethod
    def get_ollama_url(cls) -> str:
        """获取Ollama API URL"""
        return f"{cls.OLLAMA_BASE_URL}/api/generate"
    
    @classmethod
    def get_config_dict(cls) -> Dict[str, Any]:
        """获取配置字典"""
        return {
            "ollama": {
                "base_url": cls.OLLAMA_BASE_URL,
                "model_name": cls.OLLAMA_MODEL_NAME,
                "timeout": cls.OLLAMA_TIMEOUT
            },
            "speech_recognition": {
                "timeout": cls.SPEECH_RECOGNITION_TIMEOUT,
                "phrase_timeout": cls.SPEECH_RECOGNITION_PHRASE_TIMEOUT,
                "language": cls.SPEECH_RECOGNITION_LANGUAGE
            },
            "tts": {
                "rate": cls.TTS_RATE,
                "volume": cls.TTS_VOLUME,
                "voice_index": cls.TTS_VOICE_INDEX
            },
            "ui": {
                "title": cls.WINDOW_TITLE,
                "width": cls.WINDOW_WIDTH,
                "height": cls.WINDOW_HEIGHT
            },
            "logging": {
                "level": cls.LOG_LEVEL,
                "file": cls.LOG_FILE
            },
            "diagnosis": {
                "max_response_length": cls.MAX_RESPONSE_LENGTH,
                "timeout": cls.DIAGNOSIS_TIMEOUT
            }
        }

# 创建全局配置实例
config = Config()
