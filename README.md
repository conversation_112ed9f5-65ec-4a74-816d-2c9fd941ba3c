# 数字人语音诊疗系统

一个基于Python的智能语音医疗诊断系统，集成了语音识别、AI诊断和语音合成功能。

## 🎯 项目特性

- **语音输入**: 支持实时语音识别，患者可通过语音描述症状
- **智能诊断**: 使用本地部署的Ollama deepseek-r1:1.5b模型进行医疗诊断
- **语音输出**: 自动将诊断结果通过语音播报
- **紧急检测**: 自动识别紧急医疗情况并提醒
- **友好界面**: 基于tkinter的图形用户界面
- **模块化设计**: 清晰的代码结构，易于维护和扩展

## 🏗️ 项目结构

```
医疗/
├── main.py                 # 主程序入口
├── config.py              # 系统配置文件
├── requirements.txt        # 依赖包列表
├── README.md              # 项目说明文档
├── modules/               # 核心功能模块
│   ├── __init__.py
│   ├── ollama_client.py           # Ollama API客户端
│   ├── speech_recognition_module.py # 语音识别模块
│   ├── speech_synthesis_module.py   # 语音合成模块
│   ├── medical_diagnosis.py        # 医疗诊断逻辑
│   └── ui_interface.py            # 用户界面
├── prompts/               # AI提示词模板
│   ├── __init__.py
│   └── medical_prompts.py
├── assets/                # 资源文件
│   ├── audio/            # 音频文件
│   └── images/           # 图片资源
└── tests/                # 测试文件
    └── __init__.py
```

## 🔧 技术栈

- **Python 3.8+**: 主要开发语言
- **Ollama**: 本地AI模型服务
- **deepseek-r1:1.5b**: AI诊断模型
- **SpeechRecognition**: 语音识别库
- **pyttsx3**: 语音合成库
- **tkinter**: GUI界面框架
- **requests**: HTTP客户端

## 📋 系统要求

### 硬件要求
- CPU: 双核以上处理器
- 内存: 4GB RAM以上
- 存储: 2GB可用空间
- 音频: 麦克风和扬声器

### 软件要求
- Python 3.8或更高版本
- Ollama服务已安装并运行
- deepseek-r1:1.5b模型已下载

## 🚀 安装指南

### 1. 克隆项目
```bash
git clone <repository-url>
cd 医疗
```

### 2. 安装Python依赖
```bash
pip install -r requirements.txt
```

### 3. 安装和配置Ollama

#### Windows:
```bash
# 下载并安装Ollama
# 访问 https://ollama.ai 下载Windows版本

# 启动Ollama服务
ollama serve

# 下载deepseek-r1:1.5b模型
ollama pull deepseek-r1:1.5b
```

#### Linux/macOS:
```bash
# 安装Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# 启动服务
ollama serve

# 下载模型
ollama pull deepseek-r1:1.5b
```

### 4. 验证安装
```bash
# 检查Ollama服务状态
curl http://localhost:11434/api/tags

# 验证模型可用性
ollama list
```

## 🎮 使用方法

### 启动系统
```bash
python main.py
```

### 基本操作流程

1. **启动程序**: 运行main.py启动图形界面
2. **系统检查**: 程序会自动检查各组件状态
3. **语音输入**: 点击"开始语音输入"按钮
4. **描述症状**: 清晰地描述患者症状
5. **获取诊断**: 系统自动生成诊断结果
6. **语音播报**: 诊断结果会自动语音播报

### 界面说明

- **语音输入按钮**: 开始/停止语音识别
- **症状描述区**: 显示识别到的症状文本
- **诊断结果区**: 显示AI生成的诊断建议
- **状态栏**: 显示当前系统状态
- **进度条**: 显示处理进度

## ⚙️ 配置说明

主要配置项在`config.py`文件中：

```python
# Ollama服务配置
OLLAMA_BASE_URL = "http://localhost:11434"
OLLAMA_MODEL_NAME = "deepseek-r1:1.5b"

# 语音识别配置
SPEECH_RECOGNITION_LANGUAGE = "zh-CN"
SPEECH_RECOGNITION_TIMEOUT = 5

# 语音合成配置
TTS_RATE = 150  # 语音速度
TTS_VOLUME = 0.8  # 音量
```

## 🔍 故障排除

### 常见问题

1. **Ollama连接失败**
   - 确保Ollama服务正在运行: `ollama serve`
   - 检查端口11434是否被占用
   - 验证防火墙设置

2. **模型不可用**
   - 确认已下载deepseek-r1:1.5b模型: `ollama pull deepseek-r1:1.5b`
   - 检查模型列表: `ollama list`

3. **语音识别失败**
   - 检查麦克风权限
   - 确认麦克风设备正常工作
   - 检查网络连接（Google语音识别需要网络）

4. **语音合成无声音**
   - 检查系统音量设置
   - 确认扬声器设备正常
   - 检查pyttsx3引擎初始化

### 日志查看
系统会生成`medical_diagnosis.log`日志文件，包含详细的运行信息和错误记录。

## ⚠️ 重要声明

**本系统仅供学习和研究使用，不能替代专业医疗诊断！**

- 系统提供的诊断建议仅供参考
- 任何健康问题都应咨询专业医生
- 紧急情况请立即就医或拨打急救电话
- 不要仅依赖AI诊断做出医疗决策

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目：

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 📄 许可证

本项目采用MIT许可证 - 详见LICENSE文件

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**祝您使用愉快！**
