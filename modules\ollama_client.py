"""
Ollama客户端模块
Ollama Client Module

用于与本地Ollama服务进行HTTP通信，调用deepseek-r1:1.5b模型
"""

import requests
import json
import logging
from typing import Optional, Dict, Any
from config import config

class OllamaClient:
    """Ollama API客户端"""
    
    def __init__(self):
        """初始化Ollama客户端"""
        self.base_url = config.OLLAMA_BASE_URL
        self.model_name = config.OLLAMA_MODEL_NAME
        self.timeout = config.OLLAMA_TIMEOUT
        self.logger = logging.getLogger(__name__)
        
    def check_connection(self) -> bool:
        """检查Ollama服务连接状态"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except Exception as e:
            self.logger.error(f"无法连接到Ollama服务: {e}")
            return False
    
    def check_model_availability(self) -> bool:
        """检查模型是否可用"""
        try:
            response = requests.get(f"{self.base_url}/api/tags", timeout=5)
            if response.status_code == 200:
                models = response.json().get("models", [])
                for model in models:
                    if model.get("name", "").startswith(self.model_name):
                        return True
            return False
        except Exception as e:
            self.logger.error(f"检查模型可用性失败: {e}")
            return False
    
    def generate_response(self, prompt: str, stream: bool = False) -> Optional[str]:
        """
        生成AI响应
        
        Args:
            prompt: 输入提示词
            stream: 是否使用流式响应
            
        Returns:
            AI生成的响应文本，失败时返回None
        """
        try:
            # 构建请求数据
            data = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": stream,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "max_tokens": config.MAX_RESPONSE_LENGTH
                }
            }
            
            # 发送请求
            response = requests.post(
                f"{self.base_url}/api/generate",
                json=data,
                timeout=self.timeout,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                if stream:
                    return self._handle_stream_response(response)
                else:
                    result = response.json()
                    return result.get("response", "").strip()
            else:
                self.logger.error(f"Ollama API请求失败: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            self.logger.error("Ollama API请求超时")
            return None
        except requests.exceptions.ConnectionError:
            self.logger.error("无法连接到Ollama服务")
            return None
        except Exception as e:
            self.logger.error(f"Ollama API调用异常: {e}")
            return None
    
    def _handle_stream_response(self, response) -> Optional[str]:
        """处理流式响应"""
        try:
            full_response = ""
            for line in response.iter_lines():
                if line:
                    data = json.loads(line.decode('utf-8'))
                    if 'response' in data:
                        full_response += data['response']
                    if data.get('done', False):
                        break
            return full_response.strip()
        except Exception as e:
            self.logger.error(f"处理流式响应失败: {e}")
            return None
    
    def get_model_info(self) -> Optional[Dict[str, Any]]:
        """获取模型信息"""
        try:
            response = requests.post(
                f"{self.base_url}/api/show",
                json={"name": self.model_name},
                timeout=10
            )
            if response.status_code == 200:
                return response.json()
            return None
        except Exception as e:
            self.logger.error(f"获取模型信息失败: {e}")
            return None
