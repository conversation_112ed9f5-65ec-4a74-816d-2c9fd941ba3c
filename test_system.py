"""
数字人语音诊疗系统快速测试脚本
Quick test script for Digital Human Voice Medical Diagnosis System
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.ollama_client import OllamaClient
from modules.medical_diagnosis import MedicalDiagnosisSystem
from modules.speech_synthesis_module import SpeechSynthesizer

def test_ollama_connection():
    """测试Ollama连接"""
    print("🔍 测试Ollama连接...")
    client = OllamaClient()
    
    connection = client.check_connection()
    model_available = client.check_model_availability()
    
    print(f"   Ollama服务连接: {'✅ 正常' if connection else '❌ 失败'}")
    print(f"   deepseek-r1:1.5b模型: {'✅ 可用' if model_available else '❌ 不可用'}")
    
    return connection and model_available

def test_speech_synthesis():
    """测试语音合成"""
    print("\n🔊 测试语音合成...")
    try:
        synthesizer = SpeechSynthesizer()
        if synthesizer.check_engine():
            print("   语音合成引擎: ✅ 正常")
            
            # 测试语音播报
            print("   正在测试语音播报...")
            synthesizer.speak("系统测试，语音合成功能正常", async_mode=False)
            print("   语音播报: ✅ 完成")
            
            synthesizer.cleanup()
            return True
        else:
            print("   语音合成引擎: ❌ 初始化失败")
            return False
    except Exception as e:
        print(f"   语音合成测试失败: ❌ {e}")
        return False

def test_diagnosis_system():
    """测试诊断系统"""
    print("\n🏥 测试医疗诊断系统...")
    try:
        diagnosis_system = MedicalDiagnosisSystem()
        
        # 检查系统状态
        status = diagnosis_system.check_system_status()
        print(f"   系统状态检查: {'✅ 正常' if all(status.values()) else '⚠️ 部分异常'}")
        
        # 测试症状预处理
        test_symptoms = "  头痛   发热  咳嗽  "
        processed = diagnosis_system.preprocess_symptoms(test_symptoms)
        print(f"   症状预处理: ✅ 正常 ('{test_symptoms.strip()}' -> '{processed}')")
        
        # 测试紧急情况检测
        emergency_test = diagnosis_system.detect_emergency("胸痛、呼吸困难")
        normal_test = diagnosis_system.detect_emergency("轻微头痛")
        print(f"   紧急情况检测: ✅ 正常 (紧急: {emergency_test}, 普通: {normal_test})")
        
        return True
    except Exception as e:
        print(f"   诊断系统测试失败: ❌ {e}")
        return False

def test_full_diagnosis():
    """测试完整诊断流程"""
    print("\n🩺 测试完整诊断流程...")
    try:
        diagnosis_system = MedicalDiagnosisSystem()
        
        # 测试症状
        test_symptoms = "头痛、发热38度、咳嗽、乏力"
        print(f"   测试症状: {test_symptoms}")
        
        # 生成诊断
        print("   正在生成AI诊断...")
        result = diagnosis_system.generate_diagnosis(test_symptoms)
        
        if result["success"]:
            print("   AI诊断生成: ✅ 成功")
            print(f"   紧急程度: {'⚠️ 紧急' if result['is_emergency'] else '✅ 非紧急'}")
            print(f"   诊断结果预览: {result['diagnosis'][:100]}...")
            
            # 测试语音格式化
            speech_text = diagnosis_system.format_diagnosis_for_speech(result)
            print(f"   语音格式化: ✅ 成功 (长度: {len(speech_text)} 字符)")
            
            return True
        else:
            print(f"   AI诊断生成: ❌ 失败 - {result['error']}")
            return False
            
    except Exception as e:
        print(f"   完整诊断测试失败: ❌ {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("🏥 数字人语音诊疗系统 - 功能测试")
    print("=" * 60)
    
    # 运行各项测试
    tests = [
        ("Ollama连接", test_ollama_connection),
        ("语音合成", test_speech_synthesis),
        ("诊断系统", test_diagnosis_system),
        ("完整诊断", test_full_diagnosis)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   测试异常: ❌ {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！系统运行正常。")
        print("\n💡 您现在可以运行 'python main.py' 启动完整的语音诊疗系统。")
    else:
        print("⚠️ 部分测试失败，请检查相关配置。")
        print("\n🔧 常见问题解决方案:")
        print("   1. 确保Ollama服务正在运行: ollama serve")
        print("   2. 确保已下载模型: ollama pull deepseek-r1:1.5b")
        print("   3. 检查网络连接（语音识别需要）")
        print("   4. 检查音频设备（麦克风和扬声器）")

if __name__ == "__main__":
    main()
