"""
数字人语音诊疗系统主程序
Digital Human Voice Medical Diagnosis System Main Program

整合所有模块，实现完整的语音诊疗流程
"""

import logging
import threading
import sys
import os
from typing import Optional

# 添加模块路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from modules.ui_interface import MedicalDiagnosisUI
from modules.speech_recognition_module import SpeechRecognizer
from modules.speech_synthesis_module import SpeechSynthesizer
from modules.medical_diagnosis import MedicalDiagnosisSystem
from config import config

class MedicalDiagnosisApp:
    """数字人语音诊疗系统主应用"""
    
    def __init__(self):
        """初始化应用"""
        # 设置日志
        self._setup_logging()
        self.logger = logging.getLogger(__name__)
        
        # 初始化组件
        self.ui = MedicalDiagnosisUI()
        self.speech_recognizer = SpeechRecognizer()
        self.speech_synthesizer = SpeechSynthesizer()
        self.diagnosis_system = MedicalDiagnosisSystem()
        
        # 状态变量
        self.stop_event = threading.Event()
        self.current_listen_thread: Optional[threading.Thread] = None
        
        # 设置回调函数
        self._setup_callbacks()
        
        # 检查系统状态
        self._check_system_status()
    
    def _setup_logging(self):
        """设置日志系统"""
        logging.basicConfig(
            level=getattr(logging, config.LOG_LEVEL),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(config.LOG_FILE, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
    
    def _setup_callbacks(self):
        """设置回调函数"""
        self.ui.set_voice_input_callback(self._on_voice_input)
        self.ui.set_stop_voice_callback(self._on_stop_voice)
        self.ui.set_clear_callback(self._on_clear)
    
    def _check_system_status(self):
        """检查系统状态"""
        self.logger.info("检查系统状态...")
        
        # 检查各组件状态
        status_checks = {
            "麦克风": self.speech_recognizer.check_microphone(),
            "语音合成": self.speech_synthesizer.check_engine(),
            "Ollama连接": self.diagnosis_system.ollama_client.check_connection(),
            "AI模型": self.diagnosis_system.ollama_client.check_model_availability()
        }
        
        # 显示状态
        failed_components = []
        for component, status in status_checks.items():
            if status:
                self.logger.info(f"{component}: 正常")
            else:
                self.logger.error(f"{component}: 异常")
                failed_components.append(component)
        
        if failed_components:
            error_msg = f"以下组件异常: {', '.join(failed_components)}"
            self.ui.update_status(f"系统异常: {error_msg}")
            self.ui.show_warning("系统检查", error_msg + "\n\n请检查相关配置后重试。")
        else:
            self.ui.update_status("系统就绪，可以开始语音诊疗")
            self.logger.info("系统检查完成，所有组件正常")
    
    def _on_voice_input(self):
        """语音输入回调"""
        if self.current_listen_thread and self.current_listen_thread.is_alive():
            return
        
        self.stop_event.clear()
        self.ui.set_listening_state(True)
        self.ui.update_status("正在监听语音输入...")
        
        # 在新线程中进行语音识别
        self.current_listen_thread = threading.Thread(
            target=self._voice_recognition_thread,
            daemon=True
        )
        self.current_listen_thread.start()
    
    def _voice_recognition_thread(self):
        """语音识别线程"""
        try:
            # 进行语音识别
            recognized_text = self.speech_recognizer.listen_once(
                timeout=config.SPEECH_RECOGNITION_TIMEOUT
            )
            
            if recognized_text:
                self.logger.info(f"识别到语音: {recognized_text}")
                
                # 更新界面
                current_text = self.ui.get_symptoms_text()
                if current_text:
                    new_text = current_text + " " + recognized_text
                else:
                    new_text = recognized_text
                
                self.ui.set_symptoms_text(new_text)
                self.ui.update_status("语音识别完成，正在生成诊断...")
                
                # 开始诊断
                self._start_diagnosis(new_text)
            else:
                self.ui.update_status("未识别到语音，请重试")
                
        except Exception as e:
            self.logger.error(f"语音识别异常: {e}")
            self.ui.update_status(f"语音识别失败: {str(e)}")
            self.ui.show_error("语音识别错误", f"语音识别过程中发生错误:\n{str(e)}")
        
        finally:
            self.ui.set_listening_state(False)
    
    def _start_diagnosis(self, symptoms: str):
        """开始诊断"""
        if not symptoms.strip():
            self.ui.update_status("症状描述为空")
            return
        
        self.ui.set_processing_state(True)
        
        # 在新线程中进行诊断
        diagnosis_thread = threading.Thread(
            target=self._diagnosis_thread,
            args=(symptoms,),
            daemon=True
        )
        diagnosis_thread.start()
    
    def _diagnosis_thread(self, symptoms: str):
        """诊断线程"""
        try:
            self.logger.info(f"开始诊断，症状: {symptoms}")
            
            # 生成诊断
            diagnosis_result = self.diagnosis_system.generate_diagnosis(symptoms)
            
            if diagnosis_result["success"]:
                diagnosis_text = diagnosis_result["diagnosis"]
                is_emergency = diagnosis_result["is_emergency"]
                
                # 显示诊断结果
                self.ui.set_result_text(diagnosis_text)
                
                # 语音播报
                speech_text = self.diagnosis_system.format_diagnosis_for_speech(diagnosis_result)
                self.speech_synthesizer.speak(speech_text, async_mode=True)
                
                # 更新状态
                if is_emergency:
                    self.ui.update_status("诊断完成 - 检测到紧急情况！")
                    self.ui.show_warning("紧急提醒", "检测到可能的紧急情况，建议立即就医！")
                else:
                    self.ui.update_status("诊断完成")
                
                self.logger.info("诊断完成")
                
            else:
                error_msg = diagnosis_result.get("error", "未知错误")
                self.ui.set_result_text(f"诊断失败: {error_msg}")
                self.ui.update_status(f"诊断失败: {error_msg}")
                self.ui.show_error("诊断错误", f"诊断过程中发生错误:\n{error_msg}")
                
        except Exception as e:
            self.logger.error(f"诊断异常: {e}")
            self.ui.set_result_text(f"诊断异常: {str(e)}")
            self.ui.update_status(f"诊断异常: {str(e)}")
            self.ui.show_error("诊断异常", f"诊断过程中发生异常:\n{str(e)}")
        
        finally:
            self.ui.set_processing_state(False)
    
    def _on_stop_voice(self):
        """停止语音回调"""
        self.stop_event.set()
        self.speech_recognizer.stop_listening()
        self.speech_synthesizer.stop_speaking()
        
        self.ui.set_listening_state(False)
        self.ui.set_processing_state(False)
        self.ui.update_status("操作已停止")
        
        self.logger.info("用户停止操作")
    
    def _on_clear(self):
        """清除回调"""
        self.speech_synthesizer.stop_speaking()
        self.ui.update_status("界面已清除")
        self.logger.info("界面已清除")
    
    def run(self):
        """运行应用"""
        try:
            self.logger.info("启动数字人语音诊疗系统")
            self.ui.run()
        except KeyboardInterrupt:
            self.logger.info("用户中断程序")
        except Exception as e:
            self.logger.error(f"程序运行异常: {e}")
        finally:
            self._cleanup()
    
    def _cleanup(self):
        """清理资源"""
        try:
            self.logger.info("清理系统资源...")
            
            # 停止所有操作
            self.stop_event.set()
            self.speech_recognizer.stop_listening()
            self.speech_synthesizer.cleanup()
            
            # 等待线程结束
            if self.current_listen_thread and self.current_listen_thread.is_alive():
                self.current_listen_thread.join(timeout=2)
            
            self.logger.info("资源清理完成")
            
        except Exception as e:
            self.logger.error(f"资源清理异常: {e}")

def main():
    """主函数"""
    try:
        app = MedicalDiagnosisApp()
        app.run()
    except Exception as e:
        print(f"程序启动失败: {e}")
        logging.error(f"程序启动失败: {e}")

if __name__ == "__main__":
    main()
