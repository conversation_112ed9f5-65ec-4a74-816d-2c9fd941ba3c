2025-07-28 00:28:41,351 - modules.speech_recognition_module - INFO - 正在调整环境噪音...
2025-07-28 00:28:42,408 - modules.speech_recognition_module - INFO - 环境噪音调整完成
2025-07-28 00:28:42,474 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-28 00:28:42,474 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\comtypes\gen'
2025-07-28 00:28:42,683 - modules.speech_synthesis_module - INFO - 语音合成引擎初始化成功
2025-07-28 00:28:42,684 - __main__ - INFO - 检查系统状态...
2025-07-28 00:28:48,829 - __main__ - INFO - 麦克风: 正常
2025-07-28 00:28:48,829 - __main__ - INFO - 语音合成: 正常
2025-07-28 00:28:48,829 - __main__ - INFO - Ollama连接: 正常
2025-07-28 00:28:48,829 - __main__ - INFO - AI模型: 正常
2025-07-28 00:28:48,829 - __main__ - INFO - 系统检查完成，所有组件正常
2025-07-28 00:28:48,829 - __main__ - INFO - 启动数字人语音诊疗系统
2025-07-28 00:28:48,836 - modules.ui_interface - INFO - 状态更新: 系统就绪，可以开始语音诊疗
2025-07-28 00:28:53,717 - modules.ui_interface - INFO - 状态更新: 正在监听语音输入...
2025-07-28 00:28:53,776 - modules.speech_recognition_module - INFO - 请开始说话...
2025-07-28 00:28:57,302 - modules.speech_recognition_module - INFO - 正在识别语音...
2025-07-28 00:28:57,575 - modules.speech_recognition_module - INFO - 语音识别已停止
2025-07-28 00:28:57,577 - __main__ - INFO - 用户停止操作
2025-07-28 00:28:57,581 - modules.ui_interface - INFO - 状态更新: 操作已停止
2025-07-28 00:28:58,208 - modules.speech_recognition_module - WARNING - 无法识别语音内容
2025-07-28 00:28:58,212 - modules.ui_interface - INFO - 状态更新: 未识别到语音，请重试
2025-07-28 00:29:50,068 - modules.ui_interface - INFO - 状态更新: 正在监听语音输入...
2025-07-28 00:29:50,132 - modules.speech_recognition_module - INFO - 请开始说话...
2025-07-28 00:29:51,357 - modules.speech_recognition_module - INFO - 正在识别语音...
2025-07-28 00:29:51,854 - modules.speech_recognition_module - INFO - 识别结果: 感
2025-07-28 00:29:51,857 - __main__ - INFO - 识别到语音: 感
2025-07-28 00:29:51,861 - __main__ - INFO - 开始诊断，症状: 感
2025-07-28 00:29:51,865 - modules.ui_interface - INFO - 状态更新: 语音识别完成，正在生成诊断...
2025-07-28 00:29:56,904 - modules.medical_diagnosis - WARNING - AI检测到紧急情况
2025-07-28 00:30:03,946 - modules.speech_synthesis_module - INFO - 开始播报: 注意：检测到可能的紧急情况，建议立即就医。根据您描述的症状，我的初步分析如下：<think> 好的，...
2025-07-28 00:30:03,950 - modules.ui_interface - INFO - 状态更新: 诊断完成 - 检测到紧急情况！
2025-07-28 00:30:03,950 - __main__ - INFO - 诊断完成
2025-07-28 00:30:09,792 - modules.ui_interface - INFO - 状态更新: 正在监听语音输入...
2025-07-28 00:30:09,864 - modules.speech_recognition_module - INFO - 请开始说话...
2025-07-28 00:30:11,689 - modules.speech_recognition_module - INFO - 正在识别语音...
2025-07-28 00:30:12,310 - modules.speech_recognition_module - INFO - 识别结果: 感冒发
2025-07-28 00:30:12,312 - __main__ - INFO - 识别到语音: 感冒发
2025-07-28 00:30:12,313 - modules.ui_interface - INFO - 状态更新: 语音识别完成，正在生成诊断...
2025-07-28 00:30:12,313 - __main__ - INFO - 开始诊断，症状: 感 感冒发
2025-07-28 00:30:16,266 - modules.medical_diagnosis - WARNING - AI检测到紧急情况
2025-07-28 00:30:23,567 - __main__ - INFO - 诊断完成
2025-07-28 00:30:23,568 - modules.ui_interface - INFO - 状态更新: 诊断完成 - 检测到紧急情况！
2025-07-28 00:31:52,987 - modules.speech_recognition_module - INFO - 正在调整环境噪音...
2025-07-28 00:31:54,044 - modules.speech_recognition_module - INFO - 环境噪音调整完成
2025-07-28 00:31:54,093 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python312\\site-packages\\comtypes\\gen\\__init__.py'>
2025-07-28 00:31:54,094 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\comtypes\gen'
2025-07-28 00:31:54,193 - modules.speech_synthesis_module - INFO - 语音合成引擎初始化成功
2025-07-28 00:31:54,193 - __main__ - INFO - 检查系统状态...
2025-07-28 00:31:59,813 - __main__ - INFO - 麦克风: 正常
2025-07-28 00:31:59,813 - __main__ - INFO - 语音合成: 正常
2025-07-28 00:31:59,813 - __main__ - INFO - Ollama连接: 正常
2025-07-28 00:31:59,813 - __main__ - INFO - AI模型: 正常
2025-07-28 00:31:59,813 - __main__ - INFO - 系统检查完成，所有组件正常
2025-07-28 00:31:59,813 - __main__ - INFO - 启动数字人语音诊疗系统
2025-07-28 00:31:59,813 - modules.ui_interface - INFO - 状态更新: 系统就绪，可以开始语音诊疗
2025-07-28 00:32:22,398 - __main__ - INFO - 清理系统资源...
2025-07-28 00:32:22,399 - modules.speech_recognition_module - INFO - 语音识别已停止
2025-07-28 00:32:22,400 - modules.speech_synthesis_module - INFO - 语音合成器资源清理完成
2025-07-28 00:32:22,400 - __main__ - INFO - 资源清理完成
2025-07-28 00:34:37,657 - modules.speech_synthesis_module - INFO - 播报完成
2025-07-28 00:34:37,657 - modules.speech_synthesis_module - INFO - 开始播报: 注意：检测到可能的紧急情况，建议立即就医。根据您描述的症状，我的初步分析如下：<think> 嗯，这...
2025-07-28 00:34:54,623 - __main__ - INFO - 清理系统资源...
2025-07-28 00:34:54,623 - modules.speech_recognition_module - INFO - 语音识别已停止
2025-07-28 00:34:54,681 - modules.speech_synthesis_module - INFO - 语音播报已停止
2025-07-28 00:34:56,696 - modules.speech_synthesis_module - INFO - 语音合成器资源清理完成
2025-07-28 00:34:56,696 - __main__ - INFO - 资源清理完成
