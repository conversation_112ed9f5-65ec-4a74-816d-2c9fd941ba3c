"""
语音识别模块
Speech Recognition Module

使用speech_recognition库实现语音转文本功能
"""

import speech_recognition as sr
import logging
import threading
from typing import Optional, Callable
from config import config

class SpeechRecognizer:
    """语音识别器"""
    
    def __init__(self):
        """初始化语音识别器"""
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        self.logger = logging.getLogger(__name__)
        self.is_listening = False
        
        # 配置识别器参数
        self.recognizer.energy_threshold = 300
        self.recognizer.dynamic_energy_threshold = True
        self.recognizer.pause_threshold = 0.8
        self.recognizer.phrase_threshold = 0.3
        
        # 调整麦克风环境噪音
        self._adjust_for_ambient_noise()
    
    def _adjust_for_ambient_noise(self):
        """调整环境噪音"""
        try:
            with self.microphone as source:
                self.logger.info("正在调整环境噪音...")
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
                self.logger.info("环境噪音调整完成")
        except Exception as e:
            self.logger.error(f"调整环境噪音失败: {e}")
    
    def check_microphone(self) -> bool:
        """检查麦克风是否可用"""
        try:
            with self.microphone as source:
                # 尝试录制一小段音频
                audio = self.recognizer.listen(source, timeout=1, phrase_time_limit=1)
                return True
        except Exception as e:
            self.logger.error(f"麦克风检查失败: {e}")
            return False
    
    def listen_once(self, timeout: Optional[float] = None) -> Optional[str]:
        """
        单次语音识别
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            识别到的文本，失败时返回None
        """
        try:
            timeout = timeout or config.SPEECH_RECOGNITION_TIMEOUT
            
            with self.microphone as source:
                self.logger.info("请开始说话...")
                # 监听音频
                audio = self.recognizer.listen(
                    source, 
                    timeout=timeout,
                    phrase_time_limit=config.SPEECH_RECOGNITION_PHRASE_TIMEOUT
                )
                
                self.logger.info("正在识别语音...")
                # 使用Google语音识别
                text = self.recognizer.recognize_google(
                    audio, 
                    language=config.SPEECH_RECOGNITION_LANGUAGE
                )
                
                self.logger.info(f"识别结果: {text}")
                return text
                
        except sr.WaitTimeoutError:
            self.logger.warning("语音识别超时")
            return None
        except sr.UnknownValueError:
            self.logger.warning("无法识别语音内容")
            return None
        except sr.RequestError as e:
            self.logger.error(f"语音识别服务错误: {e}")
            return None
        except Exception as e:
            self.logger.error(f"语音识别异常: {e}")
            return None
    
    def listen_continuously(self, callback: Callable[[str], None], 
                          stop_event: threading.Event):
        """
        连续语音识别
        
        Args:
            callback: 识别结果回调函数
            stop_event: 停止事件
        """
        self.is_listening = True
        
        def listen_loop():
            while not stop_event.is_set() and self.is_listening:
                try:
                    with self.microphone as source:
                        # 监听音频
                        audio = self.recognizer.listen(
                            source, 
                            timeout=1,
                            phrase_time_limit=config.SPEECH_RECOGNITION_PHRASE_TIMEOUT
                        )
                        
                        # 识别语音
                        text = self.recognizer.recognize_google(
                            audio, 
                            language=config.SPEECH_RECOGNITION_LANGUAGE
                        )
                        
                        if text.strip():
                            callback(text)
                            
                except sr.WaitTimeoutError:
                    continue
                except sr.UnknownValueError:
                    continue
                except sr.RequestError as e:
                    self.logger.error(f"语音识别服务错误: {e}")
                    break
                except Exception as e:
                    self.logger.error(f"连续语音识别异常: {e}")
                    break
        
        # 在新线程中运行监听循环
        listen_thread = threading.Thread(target=listen_loop, daemon=True)
        listen_thread.start()
        return listen_thread
    
    def stop_listening(self):
        """停止语音识别"""
        self.is_listening = False
        self.logger.info("语音识别已停止")
    
    def get_available_microphones(self) -> list:
        """获取可用的麦克风列表"""
        try:
            mic_list = []
            for index, name in enumerate(sr.Microphone.list_microphone_names()):
                mic_list.append({"index": index, "name": name})
            return mic_list
        except Exception as e:
            self.logger.error(f"获取麦克风列表失败: {e}")
            return []
    
    def set_microphone(self, device_index: int):
        """设置麦克风设备"""
        try:
            self.microphone = sr.Microphone(device_index=device_index)
            self._adjust_for_ambient_noise()
            self.logger.info(f"已切换到麦克风设备: {device_index}")
        except Exception as e:
            self.logger.error(f"设置麦克风失败: {e}")
