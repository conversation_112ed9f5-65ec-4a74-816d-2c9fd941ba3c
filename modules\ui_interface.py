"""
用户界面模块
User Interface Module

使用tkinter创建图形用户界面
"""

import tkinter as tk
from tkinter import ttk, scrolledtext, messagebox
import threading
import logging
from typing import Optional, Callable
from config import config

class MedicalDiagnosisUI:
    """医疗诊断系统用户界面"""
    
    def __init__(self):
        """初始化用户界面"""
        self.root = tk.Tk()
        self.logger = logging.getLogger(__name__)
        
        # 回调函数
        self.on_voice_input_callback: Optional[Callable] = None
        self.on_stop_voice_callback: Optional[Callable] = None
        self.on_clear_callback: Optional[Callable] = None
        
        # 界面状态
        self.is_listening = False
        self.is_processing = False
        
        # 初始化界面
        self._setup_ui()
        
    def _setup_ui(self):
        """设置用户界面"""
        # 窗口配置
        self.root.title(config.WINDOW_TITLE)
        self.root.geometry(f"{config.WINDOW_WIDTH}x{config.WINDOW_HEIGHT}")
        self.root.resizable(True, True)
        
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="数字人语音诊疗系统", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 控制按钮框架
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=1, column=0, columnspan=3, pady=(0, 10), sticky=(tk.W, tk.E))
        control_frame.columnconfigure(1, weight=1)
        
        # 语音输入按钮
        self.voice_button = ttk.Button(control_frame, text="开始语音输入", 
                                      command=self._on_voice_button_click)
        self.voice_button.grid(row=0, column=0, padx=(0, 10))
        
        # 停止按钮
        self.stop_button = ttk.Button(control_frame, text="停止", 
                                     command=self._on_stop_button_click,
                                     state="disabled")
        self.stop_button.grid(row=0, column=1, padx=(0, 10))
        
        # 清除按钮
        self.clear_button = ttk.Button(control_frame, text="清除", 
                                      command=self._on_clear_button_click)
        self.clear_button.grid(row=0, column=2)
        
        # 状态显示
        self.status_var = tk.StringVar(value="系统就绪")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, 
                                font=("Arial", 10))
        status_label.grid(row=1, column=0, columnspan=3, pady=(30, 10), sticky=tk.W)
        
        # 症状输入区域
        symptoms_label = ttk.Label(main_frame, text="症状描述：", font=("Arial", 12))
        symptoms_label.grid(row=2, column=0, sticky=(tk.W, tk.N), pady=(0, 5))
        
        self.symptoms_text = scrolledtext.ScrolledText(main_frame, height=6, 
                                                      font=("Arial", 11))
        self.symptoms_text.grid(row=2, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), 
                               pady=(0, 10))
        
        # 分隔线
        separator = ttk.Separator(main_frame, orient='horizontal')
        separator.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        # 诊断结果区域
        result_label = ttk.Label(main_frame, text="诊断结果：", font=("Arial", 12))
        result_label.grid(row=4, column=0, sticky=(tk.W, tk.N), pady=(0, 5))
        
        self.result_text = scrolledtext.ScrolledText(main_frame, height=10, 
                                                    font=("Arial", 11),
                                                    state="disabled")
        self.result_text.grid(row=4, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
    def _on_voice_button_click(self):
        """语音输入按钮点击事件"""
        if self.on_voice_input_callback:
            self.on_voice_input_callback()
    
    def _on_stop_button_click(self):
        """停止按钮点击事件"""
        if self.on_stop_voice_callback:
            self.on_stop_voice_callback()
    
    def _on_clear_button_click(self):
        """清除按钮点击事件"""
        self.symptoms_text.delete(1.0, tk.END)
        self.result_text.config(state="normal")
        self.result_text.delete(1.0, tk.END)
        self.result_text.config(state="disabled")
        
        if self.on_clear_callback:
            self.on_clear_callback()
    
    def set_voice_input_callback(self, callback: Callable):
        """设置语音输入回调函数"""
        self.on_voice_input_callback = callback
    
    def set_stop_voice_callback(self, callback: Callable):
        """设置停止语音回调函数"""
        self.on_stop_voice_callback = callback
    
    def set_clear_callback(self, callback: Callable):
        """设置清除回调函数"""
        self.on_clear_callback = callback
    
    def update_status(self, status: str):
        """更新状态显示"""
        def update():
            self.status_var.set(status)
            self.logger.info(f"状态更新: {status}")
        
        self.root.after(0, update)
    
    def set_listening_state(self, is_listening: bool):
        """设置监听状态"""
        def update():
            self.is_listening = is_listening
            if is_listening:
                self.voice_button.config(text="正在监听...", state="disabled")
                self.stop_button.config(state="normal")
                self.progress.start()
            else:
                self.voice_button.config(text="开始语音输入", state="normal")
                self.stop_button.config(state="disabled")
                self.progress.stop()
        
        self.root.after(0, update)
    
    def set_processing_state(self, is_processing: bool):
        """设置处理状态"""
        def update():
            self.is_processing = is_processing
            if is_processing:
                self.voice_button.config(state="disabled")
                self.progress.start()
            else:
                self.voice_button.config(state="normal")
                self.progress.stop()
        
        self.root.after(0, update)
    
    def add_symptoms_text(self, text: str):
        """添加症状文本"""
        def update():
            self.symptoms_text.insert(tk.END, text)
            self.symptoms_text.see(tk.END)
        
        self.root.after(0, update)
    
    def set_symptoms_text(self, text: str):
        """设置症状文本"""
        def update():
            self.symptoms_text.delete(1.0, tk.END)
            self.symptoms_text.insert(1.0, text)
        
        self.root.after(0, update)
    
    def get_symptoms_text(self) -> str:
        """获取症状文本"""
        return self.symptoms_text.get(1.0, tk.END).strip()
    
    def set_result_text(self, text: str):
        """设置诊断结果文本"""
        def update():
            self.result_text.config(state="normal")
            self.result_text.delete(1.0, tk.END)
            self.result_text.insert(1.0, text)
            self.result_text.config(state="disabled")
        
        self.root.after(0, update)
    
    def add_result_text(self, text: str):
        """添加诊断结果文本"""
        def update():
            self.result_text.config(state="normal")
            self.result_text.insert(tk.END, text)
            self.result_text.see(tk.END)
            self.result_text.config(state="disabled")
        
        self.root.after(0, update)
    
    def show_error(self, title: str, message: str):
        """显示错误对话框"""
        def show():
            messagebox.showerror(title, message)
        
        self.root.after(0, show)
    
    def show_warning(self, title: str, message: str):
        """显示警告对话框"""
        def show():
            messagebox.showwarning(title, message)
        
        self.root.after(0, show)
    
    def show_info(self, title: str, message: str):
        """显示信息对话框"""
        def show():
            messagebox.showinfo(title, message)
        
        self.root.after(0, show)
    
    def run(self):
        """运行界面主循环"""
        self.root.mainloop()
    
    def destroy(self):
        """销毁界面"""
        self.root.destroy()
