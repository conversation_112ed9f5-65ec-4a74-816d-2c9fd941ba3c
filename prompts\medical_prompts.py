"""
医疗诊断提示词模板
Medical Diagnosis Prompts Templates
"""

class MedicalPrompts:
    """医疗诊断提示词类"""
    
    # 基础诊断提示词模板
    BASIC_DIAGNOSIS_TEMPLATE = """你是一位经验丰富的医疗助手，具有丰富的临床经验。请根据患者描述的症状，提供专业的初步诊断建议。

患者症状描述：{symptoms}

请按以下格式提供建议：

**可能的疾病或病因：**
- 列出2-3个最可能的疾病或病因
- 简要说明每个可能性的依据

**建议的检查项目：**
- 推荐必要的检查项目
- 说明检查的目的

**注意事项和建议：**
- 生活方式建议
- 饮食注意事项
- 其他护理建议

**紧急程度评估：**
- 是否需要立即就医
- 如果不紧急，建议的就医时间

**重要提醒：**
此建议仅供参考，不能替代专业医生的诊断。请及时到正规医疗机构就诊，获得准确的诊断和治疗方案。

请用简洁、专业但易懂的语言回复，避免使用过于复杂的医学术语。"""

    # 紧急情况识别提示词
    EMERGENCY_DETECTION_TEMPLATE = """请分析以下症状是否属于紧急医疗情况：

症状：{symptoms}

如果是紧急情况，请立即回复"紧急情况"并说明原因。
如果不是紧急情况，请回复"非紧急情况"。

紧急情况包括但不限于：
- 胸痛、呼吸困难
- 严重外伤、大量出血
- 意识丧失、昏迷
- 高热不退（体温超过39.5°C）
- 剧烈腹痛
- 中毒症状
- 严重过敏反应
- 突发性剧烈头痛
- 言语不清、肢体无力等中风症状"""

    # 症状分类提示词
    SYMPTOM_CLASSIFICATION_TEMPLATE = """请将以下症状按照身体系统进行分类：

症状：{symptoms}

请按以下系统分类：
1. 呼吸系统
2. 消化系统  
3. 心血管系统
4. 神经系统
5. 泌尿系统
6. 皮肤系统
7. 肌肉骨骼系统
8. 内分泌系统
9. 其他

只需简单列出相关系统，不需要详细解释。"""

    # 随访建议提示词
    FOLLOWUP_TEMPLATE = """基于患者的症状：{symptoms}

以及初步诊断：{diagnosis}

请提供详细的随访建议：

**短期随访（1-3天内）：**
- 需要观察的症状变化
- 何时需要立即就医

**中期随访（1-2周内）：**
- 建议的复查项目
- 预期的恢复情况

**长期管理建议：**
- 预防措施
- 生活方式调整
- 定期检查建议

请提供具体、可操作的建议。"""

    # 药物咨询提示词
    MEDICATION_CONSULTATION_TEMPLATE = """患者症状：{symptoms}

请提供用药建议（仅供参考）：

**可能有帮助的非处方药物：**
- 药物名称和作用
- 使用方法和注意事项

**需要避免的药物：**
- 可能加重症状的药物

**重要提醒：**
- 任何药物使用都应咨询医生或药师
- 注意药物过敏史
- 严格按照说明书使用

请不要推荐处方药，只建议常见的非处方药物。"""

    @classmethod
    def get_diagnosis_prompt(cls, symptoms: str) -> str:
        """获取诊断提示词"""
        return cls.BASIC_DIAGNOSIS_TEMPLATE.format(symptoms=symptoms)
    
    @classmethod
    def get_emergency_prompt(cls, symptoms: str) -> str:
        """获取紧急情况识别提示词"""
        return cls.EMERGENCY_DETECTION_TEMPLATE.format(symptoms=symptoms)
    
    @classmethod
    def get_classification_prompt(cls, symptoms: str) -> str:
        """获取症状分类提示词"""
        return cls.SYMPTOM_CLASSIFICATION_TEMPLATE.format(symptoms=symptoms)
    
    @classmethod
    def get_followup_prompt(cls, symptoms: str, diagnosis: str) -> str:
        """获取随访建议提示词"""
        return cls.FOLLOWUP_TEMPLATE.format(symptoms=symptoms, diagnosis=diagnosis)
    
    @classmethod
    def get_medication_prompt(cls, symptoms: str) -> str:
        """获取药物咨询提示词"""
        return cls.MEDICATION_CONSULTATION_TEMPLATE.format(symptoms=symptoms)
