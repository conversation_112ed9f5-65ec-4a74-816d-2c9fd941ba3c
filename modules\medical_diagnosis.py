"""
医疗诊断模块
Medical Diagnosis Module

整合Ollama客户端和医疗提示词，提供智能诊断功能
"""

import logging
import re
from typing import Optional, Dict, Any, List
from .ollama_client import OllamaClient
from prompts.medical_prompts import MedicalPrompts

class MedicalDiagnosisSystem:
    """医疗诊断系统"""
    
    def __init__(self):
        """初始化医疗诊断系统"""
        self.ollama_client = OllamaClient()
        self.logger = logging.getLogger(__name__)
        
        # 紧急关键词列表
        self.emergency_keywords = [
            "胸痛", "呼吸困难", "昏迷", "意识丧失", "大量出血", 
            "剧烈腹痛", "高烧", "中毒", "过敏", "休克",
            "心脏病", "中风", "脑梗", "心梗", "窒息"
        ]
    
    def check_system_status(self) -> Dict[str, bool]:
        """检查系统状态"""
        status = {
            "ollama_connection": self.ollama_client.check_connection(),
            "model_available": self.ollama_client.check_model_availability()
        }
        
        if not status["ollama_connection"]:
            self.logger.error("Ollama服务连接失败")
        if not status["model_available"]:
            self.logger.error("deepseek-r1:1.5b模型不可用")
            
        return status
    
    def preprocess_symptoms(self, symptoms: str) -> str:
        """预处理症状描述"""
        if not symptoms or not symptoms.strip():
            return ""
        
        # 清理文本
        symptoms = symptoms.strip()
        
        # 移除多余的空格和换行
        symptoms = re.sub(r'\s+', ' ', symptoms)
        
        # 确保以句号结尾
        if not symptoms.endswith(('。', '.', '！', '!', '？', '?')):
            symptoms += '。'
        
        return symptoms
    
    def detect_emergency(self, symptoms: str) -> bool:
        """检测是否为紧急情况"""
        symptoms_lower = symptoms.lower()
        
        # 检查紧急关键词
        for keyword in self.emergency_keywords:
            if keyword in symptoms_lower:
                self.logger.warning(f"检测到紧急关键词: {keyword}")
                return True
        
        # 使用AI进行更精确的紧急情况判断
        try:
            prompt = MedicalPrompts.get_emergency_prompt(symptoms)
            response = self.ollama_client.generate_response(prompt)
            
            if response and "紧急情况" in response:
                self.logger.warning("AI检测到紧急情况")
                return True
                
        except Exception as e:
            self.logger.error(f"紧急情况检测失败: {e}")
        
        return False
    
    def generate_diagnosis(self, symptoms: str) -> Optional[Dict[str, Any]]:
        """
        生成医疗诊断
        
        Args:
            symptoms: 患者症状描述
            
        Returns:
            诊断结果字典，包含诊断内容、紧急程度等信息
        """
        try:
            # 预处理症状
            processed_symptoms = self.preprocess_symptoms(symptoms)
            if not processed_symptoms:
                return {
                    "success": False,
                    "error": "症状描述为空",
                    "diagnosis": None,
                    "is_emergency": False
                }
            
            # 检测紧急情况
            is_emergency = self.detect_emergency(processed_symptoms)
            
            # 生成诊断提示词
            prompt = MedicalPrompts.get_diagnosis_prompt(processed_symptoms)
            
            # 调用AI模型生成诊断
            diagnosis_response = self.ollama_client.generate_response(prompt)
            
            if not diagnosis_response:
                return {
                    "success": False,
                    "error": "AI诊断生成失败",
                    "diagnosis": None,
                    "is_emergency": is_emergency
                }
            
            # 解析诊断结果
            parsed_diagnosis = self._parse_diagnosis_response(diagnosis_response)
            
            return {
                "success": True,
                "error": None,
                "diagnosis": diagnosis_response,
                "parsed_diagnosis": parsed_diagnosis,
                "is_emergency": is_emergency,
                "original_symptoms": symptoms,
                "processed_symptoms": processed_symptoms
            }
            
        except Exception as e:
            self.logger.error(f"诊断生成异常: {e}")
            return {
                "success": False,
                "error": f"诊断生成异常: {str(e)}",
                "diagnosis": None,
                "is_emergency": False
            }
    
    def _parse_diagnosis_response(self, response: str) -> Dict[str, List[str]]:
        """解析诊断响应"""
        parsed = {
            "possible_diseases": [],
            "recommended_tests": [],
            "care_suggestions": [],
            "urgency_level": "未知"
        }
        
        try:
            # 简单的文本解析
            lines = response.split('\n')
            current_section = None
            
            for line in lines:
                line = line.strip()
                if not line:
                    continue
                
                if "可能的疾病" in line or "病因" in line:
                    current_section = "possible_diseases"
                elif "建议的检查" in line or "检查项目" in line:
                    current_section = "recommended_tests"
                elif "注意事项" in line or "建议" in line:
                    current_section = "care_suggestions"
                elif "紧急程度" in line:
                    current_section = "urgency_level"
                elif line.startswith('-') or line.startswith('•'):
                    if current_section and current_section != "urgency_level":
                        parsed[current_section].append(line[1:].strip())
                elif current_section == "urgency_level" and any(word in line for word in ["紧急", "立即", "马上", "不紧急"]):
                    parsed["urgency_level"] = line
            
        except Exception as e:
            self.logger.error(f"解析诊断响应失败: {e}")
        
        return parsed
    
    def get_followup_advice(self, symptoms: str, diagnosis: str) -> Optional[str]:
        """获取随访建议"""
        try:
            prompt = MedicalPrompts.get_followup_prompt(symptoms, diagnosis)
            return self.ollama_client.generate_response(prompt)
        except Exception as e:
            self.logger.error(f"获取随访建议失败: {e}")
            return None
    
    def get_medication_advice(self, symptoms: str) -> Optional[str]:
        """获取用药建议"""
        try:
            prompt = MedicalPrompts.get_medication_prompt(symptoms)
            return self.ollama_client.generate_response(prompt)
        except Exception as e:
            self.logger.error(f"获取用药建议失败: {e}")
            return None
    
    def format_diagnosis_for_speech(self, diagnosis_result: Dict[str, Any]) -> str:
        """格式化诊断结果用于语音播报"""
        if not diagnosis_result.get("success"):
            return f"诊断失败：{diagnosis_result.get('error', '未知错误')}"
        
        diagnosis = diagnosis_result.get("diagnosis", "")
        is_emergency = diagnosis_result.get("is_emergency", False)
        
        # 构建语音播报文本
        speech_text = ""
        
        if is_emergency:
            speech_text += "注意：检测到可能的紧急情况，建议立即就医。"
        
        speech_text += "根据您描述的症状，我的初步分析如下：" + diagnosis
        
        # 简化文本，移除格式标记
        speech_text = re.sub(r'\*\*([^*]+)\*\*', r'\1', speech_text)  # 移除粗体标记
        speech_text = re.sub(r'[#*-]', '', speech_text)  # 移除其他格式标记
        speech_text = re.sub(r'\s+', ' ', speech_text)  # 合并多余空格
        
        return speech_text.strip()
