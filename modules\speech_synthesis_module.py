"""
语音合成模块
Speech Synthesis Module

使用pyttsx3实现文本转语音功能
"""

import pyttsx3
import logging
import threading
import queue
from typing import Optional, List, Dict, Any
from config import config

class SpeechSynthesizer:
    """语音合成器"""
    
    def __init__(self):
        """初始化语音合成器"""
        self.engine = None
        self.logger = logging.getLogger(__name__)
        self.is_speaking = False
        self.speech_queue = queue.Queue()
        self.worker_thread = None
        
        # 初始化TTS引擎
        self._initialize_engine()
        
        # 启动语音播放工作线程
        self._start_worker_thread()
    
    def _initialize_engine(self):
        """初始化TTS引擎"""
        try:
            self.engine = pyttsx3.init()
            
            # 设置语音参数
            self.engine.setProperty('rate', config.TTS_RATE)
            self.engine.setProperty('volume', config.TTS_VOLUME)
            
            # 设置语音
            voices = self.engine.getProperty('voices')
            if voices and len(voices) > config.TTS_VOICE_INDEX:
                self.engine.setProperty('voice', voices[config.TTS_VOICE_INDEX].id)
            
            self.logger.info("语音合成引擎初始化成功")
            
        except Exception as e:
            self.logger.error(f"语音合成引擎初始化失败: {e}")
            self.engine = None
    
    def _start_worker_thread(self):
        """启动语音播放工作线程"""
        def worker():
            while True:
                try:
                    text = self.speech_queue.get(timeout=1)
                    if text is None:  # 退出信号
                        break
                    self._speak_sync(text)
                    self.speech_queue.task_done()
                except queue.Empty:
                    continue
                except Exception as e:
                    self.logger.error(f"语音播放工作线程异常: {e}")
        
        self.worker_thread = threading.Thread(target=worker, daemon=True)
        self.worker_thread.start()
    
    def check_engine(self) -> bool:
        """检查TTS引擎是否可用"""
        return self.engine is not None
    
    def speak(self, text: str, async_mode: bool = True):
        """
        语音播报文本
        
        Args:
            text: 要播报的文本
            async_mode: 是否异步播报
        """
        if not self.engine:
            self.logger.error("语音合成引擎未初始化")
            return
        
        if not text.strip():
            self.logger.warning("播报文本为空")
            return
        
        if async_mode:
            # 异步播报，添加到队列
            self.speech_queue.put(text)
        else:
            # 同步播报
            self._speak_sync(text)
    
    def _speak_sync(self, text: str):
        """同步语音播报"""
        try:
            self.is_speaking = True
            self.logger.info(f"开始播报: {text[:50]}...")
            
            self.engine.say(text)
            self.engine.runAndWait()
            
            self.logger.info("播报完成")
            
        except Exception as e:
            self.logger.error(f"语音播报失败: {e}")
        finally:
            self.is_speaking = False
    
    def stop_speaking(self):
        """停止语音播报"""
        try:
            if self.engine and self.is_speaking:
                self.engine.stop()
                self.is_speaking = False
                
                # 清空队列
                while not self.speech_queue.empty():
                    try:
                        self.speech_queue.get_nowait()
                    except queue.Empty:
                        break
                
                self.logger.info("语音播报已停止")
        except Exception as e:
            self.logger.error(f"停止语音播报失败: {e}")
    
    def get_available_voices(self) -> List[Dict[str, Any]]:
        """获取可用的语音列表"""
        try:
            if not self.engine:
                return []
            
            voices = self.engine.getProperty('voices')
            voice_list = []
            
            for i, voice in enumerate(voices):
                voice_info = {
                    "index": i,
                    "id": voice.id,
                    "name": voice.name,
                    "languages": getattr(voice, 'languages', []),
                    "gender": getattr(voice, 'gender', 'unknown'),
                    "age": getattr(voice, 'age', 'unknown')
                }
                voice_list.append(voice_info)
            
            return voice_list
            
        except Exception as e:
            self.logger.error(f"获取语音列表失败: {e}")
            return []
    
    def set_voice(self, voice_index: int):
        """设置语音"""
        try:
            if not self.engine:
                return False
            
            voices = self.engine.getProperty('voices')
            if voices and 0 <= voice_index < len(voices):
                self.engine.setProperty('voice', voices[voice_index].id)
                self.logger.info(f"已切换到语音: {voices[voice_index].name}")
                return True
            else:
                self.logger.error(f"无效的语音索引: {voice_index}")
                return False
                
        except Exception as e:
            self.logger.error(f"设置语音失败: {e}")
            return False
    
    def set_rate(self, rate: int):
        """设置语音速度"""
        try:
            if self.engine:
                self.engine.setProperty('rate', rate)
                self.logger.info(f"语音速度已设置为: {rate}")
        except Exception as e:
            self.logger.error(f"设置语音速度失败: {e}")
    
    def set_volume(self, volume: float):
        """设置音量"""
        try:
            if self.engine:
                volume = max(0.0, min(1.0, volume))  # 限制在0.0-1.0范围内
                self.engine.setProperty('volume', volume)
                self.logger.info(f"音量已设置为: {volume}")
        except Exception as e:
            self.logger.error(f"设置音量失败: {e}")
    
    def get_current_settings(self) -> Dict[str, Any]:
        """获取当前语音设置"""
        try:
            if not self.engine:
                return {}
            
            return {
                "rate": self.engine.getProperty('rate'),
                "volume": self.engine.getProperty('volume'),
                "voice": self.engine.getProperty('voice')
            }
        except Exception as e:
            self.logger.error(f"获取语音设置失败: {e}")
            return {}
    
    def cleanup(self):
        """清理资源"""
        try:
            self.stop_speaking()
            
            # 停止工作线程
            if self.worker_thread and self.worker_thread.is_alive():
                self.speech_queue.put(None)  # 发送退出信号
                self.worker_thread.join(timeout=2)
            
            # 清理引擎
            if self.engine:
                self.engine.stop()
                
            self.logger.info("语音合成器资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理语音合成器资源失败: {e}")
